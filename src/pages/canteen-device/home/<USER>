/**
 * @file 首页
 */
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { Toast } from 'shineout-mobile';
import { getDeviceSNCode } from '../_/utils/snCode';
import { IHomeMenuItem } from './interfaces';
import { getHomeDataAPI, IGetHomeDataResponse } from './services';
import styles from './styles.less';

/**
 * 首页菜单项
 */
const menuItems: IHomeMenuItem[] = [
  {
    name: t('订单核销'),
    path: '/canteen-device/order-redeem',
    icon: 'pc-order-fill',
  },
  {
    name: t('现场收银'),
    path: '/canteen-device/on-site-scan-pay',
    icon: 'pc-money-fill',
  },
  {
    name: t('订单管理'),
    path: '/canteen-device/order-manage',
    icon: 'pc-order-manage-fill',
  },
  {
    name: t('设置'),
    path: '/canteen-device/setting',
    icon: 'pc-setting-fill',
  },
];

/**
 * 首页
 */
const Home = () => {
  usePageTitle(t('希音食堂 Lite'));
  const navigate = useNavigate();
  const [homeData, setHomeData] = useState<IGetHomeDataResponse>({});
  const [snCode, setSnCode] = useState<string>('');
  const [isDataHidden, setIsDataHidden] = useState<boolean>(false);

  // 获取设备SN码
  useEffect(() => {
    const sn = getDeviceSNCode() || '';
    setSnCode(sn);
    if (!sn) {
      Toast.fail(t('无法获取设备sn码，请联系管理员'));
    }
  }, []);

  // 获取首页数据
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (snCode) {
      setLoading(true);
      getHomeDataAPI({
        snCode: getDeviceSNCode(),
      })
        .then((res) => {
          setHomeData(res);
        })
        .catch((err) => {
          console.error(t('获取首页数据失败'), err);
          Toast.fail(t('获取首页数据失败，请重试'));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [snCode]);

  return (
    <div className={styles.container}>
      {loading ? (
        <div className={styles.loadingContainer}>
          <Icon name="pc-loading" fontSize={40} />
          <div className={styles.loadingText}>{t('加载中...')}</div>
        </div>
      ) : (
        <>
          {/* 头部信息 */}
          <div className={styles.header}>
            <div className={styles.stallInfo}>
              <div className={styles.stallLogo}>
                {homeData.merchantLogoUrl ? (
                  <img src={homeData.merchantLogoUrl} alt={t('档口logo')} />
                ) : (
                  <Icon name="pc-store-fill" fontSize={32} />
                )}
              </div>
              <div className={styles.stallText}>
                <div className={styles.stallName}>{homeData.stallName}</div>
                <div className={styles.canteenName}>
                  <Icon
                    name="m-location-shineout-fill"
                    fontSize={16}
                    style={{ marginRight: 4, flexShrink: 0 }}
                  />
                  <span>{homeData.canteenName}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 营业数据 */}
          <div className={styles.businessData}>
            <div className={styles.dataTitleWrapper}>
              <div className={styles.dataTitle}>{t('今日数据')}</div>
              <div
                className={styles.dataVisibilityIcon}
                onClick={() => setIsDataHidden(!isDataHidden)}
              >
                <Icon name={isDataHidden ? 'm-closed-eye' : 'm-eye'} fontSize={18} color="#666" />
              </div>
            </div>
            <div className={styles.dataContent}>
              <div className={styles.dataItem}>
                <div className={styles.dataValue}>
                  {isDataHidden ? '***' : homeData.totalAmount ?? '--'}
                </div>
                <div className={styles.dataLabel}>{t('今日档口收款（元）')}</div>
              </div>
              <div className={styles.dataItem}>
                <div className={styles.dataValue}>
                  {isDataHidden ? '***' : homeData.orderCount ?? '--'}
                </div>
                <div className={styles.dataLabel}>{t('今日档口核销')}</div>
              </div>
            </div>
          </div>

          {/* 菜单列表 */}
          <div className={styles.menuList}>
            {menuItems.map((item) => (
              <div key={item.name} className={styles.menuItem} onClick={() => navigate(item.path)}>
                <div className={styles.menuIcon}>
                  <Icon name={item.icon} fontSize={24} />
                </div>
                <div className={styles.menuName}>{item.name}</div>
                <Icon name="m-arrow-right-shineout" className={styles.menuArrow} fontSize={20} />
              </div>
            ))}
            <div className={styles.footer}>
              <div className={styles.snCode}>
                {t('SN码：')}
                {snCode || '--'}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Home;
