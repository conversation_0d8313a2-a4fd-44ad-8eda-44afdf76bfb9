/**
 * @file 订单核销
 */
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount } from 'ahooks';
import { Button, Toast } from 'shineout-mobile';
import { getDeviceSNCode } from '../_/utils/snCode';
import styles from './index.less';
import { getRedeemInfoAPI, IGetRedeemInfoResponse, postRedeemScanAPI } from './services';

// 使用已有的Window接口扩展
declare global {
  interface Window {
    onScanResult?: (result: string) => void;
  }
}

/**
 * 获取格式化的当前日期（如：4月11日 周一）
 */
const getFormattedDate = () => {
  const date = new Date();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekDays = [t('日'), t('一'), t('二'), t('三'), t('四'), t('五'), t('六')];
  const weekDay = weekDays[date.getDay()];
  return t(`{}月{}日 周{}`, month, day, weekDay);
};

/**
 * 订单核销页面
 */
const OrderRedeem = () => {
  usePageTitle(t('订单核销'));
  const navigate = useNavigate();

  // 档口和餐数信息
  const [redeemInfo, setRedeemInfo] = useState<IGetRedeemInfoResponse | null>(null);
  // 加载状态
  const [loading, setLoading] = useState(false);

  // 导航到首页
  const navigateToHome = usePersistFn(() => {
    navigate('/canteen-device/home');
  });

  // 导航到现场收银页面
  const navigateToScanPay = usePersistFn(() => {
    navigate('/canteen-device/on-site-scan-pay');
  });

  // 导航到设置页面
  const navigateToSetting = usePersistFn(() => {
    navigate('/canteen-device/setting');
  });

  // 获取档口和餐数信息
  const fetchRedeemInfo = usePersistFn(async () => {
    try {
      setLoading(true);
      await getRedeemInfoAPI({
        snCode: getDeviceSNCode(),
      }).then((res) => {
        setRedeemInfo(res);
      });
    } catch (error) {
      console.error(t('获取档口和餐数信息失败:'), error);
      Toast.fail(t('获取档口和餐数信息失败'));
    } finally {
      setLoading(false);
    }
  });

  // 扫码核销
  const handleScan = usePersistFn(async () => {
    try {
      // 设置扫码结果回调函数
      window.onScanResult = async (qrcode: string) => {
        if (qrcode) {
          await handleScanResult(qrcode);
        } else {
          Toast.fail(t('扫码失败，请重试'));
        }
      };

      // 调用原生扫码功能
      if (window.androidDevice) {
        window?.androidDevice?.startScan();
      } else {
        Toast.fail(t('设备不支持扫码功能'));
      }
    } catch (error) {
      console.error(t('扫码失败:'), error);
      Toast.fail(t('扫码失败，请重试'));
    }
  });

  // 处理扫码结果
  const handleScanResult = usePersistFn(async (qrcode: string) => {
    try {
      const result = await postRedeemScanAPI({
        qrcode,
        snCode: getDeviceSNCode(),
      });
      if (result) {
        // 显示成功提示
        Toast.success(t('核销成功'));

        // 更新已核销数量（乐观更新）
        if (redeemInfo) {
          setRedeemInfo({
            ...redeemInfo,
            redeemedOrder: redeemInfo.redeemedOrder + 1,
          });
        }

        // 刷新档口和餐数信息
        fetchRedeemInfo();
      } else {
        Toast.fail(t('核销失败，请重试'));
      }
    } catch (error) {
      console.error(t('核销失败:'), error);
      Toast.fail(t('核销失败，请重试'));
    }
  });

  // 滑动相关状态
  const [touchStartX, setTouchStartX] = useState(0);
  const [swipeDistance, setSwipeDistance] = useState(0);

  // 处理触摸开始事件
  const handleTouchStart = usePersistFn((e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
    setSwipeDistance(0);
  });

  // 处理触摸移动事件
  const handleTouchMove = usePersistFn((e: React.TouchEvent) => {
    if (touchStartX === 0) return;

    const currentX = e.touches[0].clientX;
    const distance = currentX - touchStartX;

    // 只允许向左滑动（负值）
    if (distance < 0) {
      // 限制最大滑动距离为屏幕宽度的40%
      const maxDistance = window.innerWidth * 0.4;
      const limitedDistance = Math.max(distance, -maxDistance);
      setSwipeDistance(limitedDistance);
    }
  });

  // 处理触摸结束事件
  const handleTouchEnd = usePersistFn((e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;

    // 计算滑动距离
    const distance = endX - touchStartX;

    // 如果向左滑动超过50像素，则导航到现场收银页面
    if (distance < -50) {
      navigateToScanPay();
    }

    // 重置滑动距离
    setSwipeDistance(0);
  });

  // 页面加载时获取档口和餐数信息
  useMount(() => {
    fetchRedeemInfo();
  });

  // 计算容器样式
  const containerStyle = {
    transform: `translateX(${swipeDistance}px)`,
  };

  return (
    <div
      className={styles.container}
      style={containerStyle}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 顶部导航栏和标签页 */}
      <div className={styles.tabs}>
        <div className={styles.backButton} onClick={navigateToHome}>
          <Icon name="arrow-left" fontSize={20} />
        </div>
        <div className={styles.tabActive}>{t('订单核销')}</div>
        <div className={styles.tab} onClick={navigateToScanPay}>
          {t('现场收银')}
        </div>
        <div className={styles.setting} onClick={navigateToSetting}>
          <Icon name="setting" fontSize={20} />
        </div>
      </div>

      {/* 扫码区域 */}
      <div className={styles.scanArea}>
        <div className={styles.scanImage}>
          <Icon name="qr-code" fontSize={120} color="#e8ebf0" />
        </div>
        {/* <div className={styles.scanTip}>{t('请扫描用户出示的取餐码')}</div> */}
      </div>

      {/* 档口信息 */}
      <div className={styles.infoCard}>
        {loading ? (
          <div className={styles.loading}>
            <Icon name="pc-loading" fontSize={40} />
          </div>
        ) : (
          <>
            <div className={styles.stallInfo}>
              <div className={styles.stallName}>{redeemInfo?.stallName}</div>
              <div className={styles.dateInfo}>
                {getFormattedDate()}
                <div className={styles.mealType}>{redeemInfo?.mealTypeName}</div>
              </div>
            </div>
            <div className={styles.orderInfo}>
              <div className={styles.orderItem}>
                <div className={styles.orderCount}>{redeemInfo?.redeemedOrder}</div>
                <div className={styles.orderLabel}>{t('已取餐')}</div>
              </div>
              <div className={styles.orderItem}>
                <div className={styles.orderCount}>{redeemInfo?.totalOrder}</div>
                <div className={styles.orderLabel}>{t('已预定')}</div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* 扫码按钮 */}
      <div className={styles.buttonArea}>
        <Button type="primary" className={styles.scanButton} onClick={handleScan}>
          {t('开始扫码核销')}
        </Button>
      </div>
    </div>
  );
};

export default OrderRedeem;
