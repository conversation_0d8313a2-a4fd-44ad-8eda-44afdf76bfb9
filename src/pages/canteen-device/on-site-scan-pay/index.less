/* 现场收银页面样式 */
.container {
  position: relative;
  display: flex;
  height: 100vh;
  overflow: hidden;
  background-color: #f4f5f8;
  flex-direction: column;

  /* 添加滑动过渡效果 */
  transition: transform .3s ease-out;
}

/* 顶部导航栏和标签页 */
.tabs {
  position: relative;
  display: flex;
  height: 50px;
  padding: 0 12px;
  background-color: #fff;
  border-bottom: 1px solid #e8ebf0;
}

.backButton {
  display: flex;
  width: 40px;
  height: 50px;
  color: #141737;
  justify-content: center;
  align-items: center;
}

.tab,
.tabActive {
  position: relative;
  display: flex;
  font-size: 16px;
  color: #666c7c;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.tabActive {
  font-weight: 500;
  color: #141737;
}

.tabActive::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60%;
  height: 2px;
  background-color: #197afa;
  content: '';
  transform: translateX(-50%);
}

.setting {
  display: flex;
  width: 40px;
  height: 50px;
  justify-content: center;
  align-items: center;
}

/* 连续收银提示 */
.continuousCollectionTip {
  display: flex;
  padding: 12px 16px;
  font-size: 14px;
  color: #197afa;
  background-color: #e6f7ff;
  align-items: center;
  gap: 8px;
}

/* 扫码弹窗 */
.scanModal {
  text-align: center;
}

.scanContent {
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: center;
}

.scanTitle {
  width: 100%;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.scanArea {
  width: 100%;
  margin-bottom: 20px;
}

.scanPlaceholder {
  display: flex;
  width: 100%;
  height: 240px;
  background-color: #333;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
}

.scanCancelButton {
  width: 100%;
  height: 48px;
  font-size: 16px;
}

/* 支付中状态 */
.payingStatus {
  display: flex;
  width: 100%;
  margin: 20px 0;
  flex-direction: column;
  align-items: center;
}

.loadingIcon {
  margin-bottom: 12px;
}

.payingText {
  font-size: 16px;
  color: #141737;
}

.countdownText {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666c7c;
}

/* 支付结果 */
.resultIcon {
  margin: 20px 0;
}

.resultText {
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 500;
  color: #141737;
}

.resultAmount {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: #ff6b00;
}

.continueButton {
  width: 100%;
  height: 48px;
  font-size: 16px;
}
