/**
 * @file 现场收银
 */
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '@/_/components/Icon';
import { usePageTitle } from '@/_/hooks';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { Button, Drawer, Toast } from 'shineout-mobile';
import AdditionCalculator from '../../canteen/_/components/addition-calculator';
import { getDeviceSNCode } from '../_/utils/snCode';
import styles from './index.less';
import { cancelOrderAPI, getOrderStatusAPI, scanPayAPI } from './services';

/**
 * 现场收银页面
 */
const OnSiteScanPay = () => {
  usePageTitle(t('现场收银'));
  const navigate = useNavigate();

  // 连续收银记录的金额
  const [amount, setAmount] = useState<string>('201.01'); // 默认值，实际应由计算器组件提供
  // 是否显示扫码弹窗
  const [showScanModal, setShowScanModal] = useState(false);
  // 支付状态：null-未支付，'paying'-支付中，'success'-支付成功，'fail'-支付失败
  const [paymentStatus, setPaymentStatus] = useState<null | 'paying' | 'success' | 'fail'>(null);
  // 倒计时（秒）
  const [countdown, setCountdown] = useState(5);
  // 支付结果信息
  const [paymentResult, setPaymentResult] = useState<{
    amount: string;
    success?: boolean;
    message?: string;
  } | null>(null);

  // 导航到首页
  const navigateToHome = usePersistFn(() => {
    navigate('/canteen-device/home');
  });

  // 导航到订单核销页面
  const navigateToOrderRedeem = usePersistFn(() => {
    navigate('/canteen-device/order-redeem');
  });

  // 导航到设置页面
  const navigateToSetting = usePersistFn(() => {
    navigate('/canteen-device/setting');
  });

  // 确认金额，触发扫码功能
  const handleConfirmAmount = usePersistFn((val: number) => {
    if (!amount || parseFloat(amount) <= 0) {
      Toast.fail(t('请输入有效金额'));
      return;
    }
    setAmount(String(val));
    handleScan();
  });

  // 取消扫码
  const handleCancelScan = usePersistFn(() => {
    setShowScanModal(false);
  });

  // 开始扫码
  const handleScan = usePersistFn(async () => {
    try {
      window.onScanResult = async (qrcode: string) => {
        if (qrcode) {
          await handleScanResult(qrcode);
        } else {
          Toast.fail(t('扫码失败，请重试'));
        }
      };

      // 调用原生扫码功能
      if (window.androidDevice) {
        window?.androidDevice?.startScan();
      } else {
        Toast.fail(t('设备不支持扫码功能'));
      }
    } catch (error) {
      console.error(t('扫码失败:'), error);
      Toast.fail(t('扫码失败，请重试'));
    }
  });

  // 处理扫码结果
  const handleScanResult = usePersistFn(async (qrcode: string) => {
    try {
      const result = await scanPayAPI({
        qrcode,
        snCode: getDeviceSNCode(),
      });
      if (result) {
        // 显示成功提示
        Toast.success(t('扫码成功'));

        setShowScanModal(true);
        startPaymentCountdown();
      } else {
        Toast.fail(t('扫码失败，请重试'));
      }
    } catch (error) {
      Toast.fail(t('扫码失败，请重试'));
    }
  });

  // 开始支付倒计时
  const startPaymentCountdown = usePersistFn(() => {
    setCountdown(5);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // 模拟支付成功
          setPaymentStatus('success');
          setPaymentResult({
            amount,
            success: true,
          });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  });

  // 取消支付
  const handleCancelPayment = usePersistFn(() => {
    setPaymentStatus(null);
    setShowScanModal(false);

    // todo
    cancelOrderAPI({
      orderNo: '123456',
      snCode: getDeviceSNCode(),
    }).then((res) => {
      if (res) {
        Toast.success(t('取消成功'));
      } else {
        Toast.fail(t('取消失败，请重试'));
      }
    });
  });

  // 继续收银
  const handleContinueCollection = usePersistFn(() => {
    setPaymentStatus(null);
    setPaymentResult(null);
    setShowScanModal(false);
  });

  // 滑动相关状态
  const [touchStartX, setTouchStartX] = useState(0);
  const [swipeDistance, setSwipeDistance] = useState(0);

  // 处理触摸开始事件
  const handleTouchStart = usePersistFn((e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
    setSwipeDistance(0);
  });

  // 处理触摸移动事件
  const handleTouchMove = usePersistFn((e: React.TouchEvent) => {
    if (touchStartX === 0) return;

    const currentX = e.touches[0].clientX;
    const distance = currentX - touchStartX;

    // 只允许向右滑动（正值）
    if (distance > 0) {
      // 限制最大滑动距离为屏幕宽度的40%
      const maxDistance = window.innerWidth * 0.4;
      const limitedDistance = Math.min(distance, maxDistance);
      setSwipeDistance(limitedDistance);
    }
  });

  // 处理触摸结束事件
  const handleTouchEnd = usePersistFn((e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;

    // 计算滑动距离
    const distance = endX - touchStartX;

    // 如果向右滑动超过50像素，则导航到订单核销页面
    if (distance > 50) {
      navigateToOrderRedeem();
    }

    // 重置滑动距离
    setSwipeDistance(0);
  });

  // 计算容器样式
  const containerStyle = {
    transform: `translateX(${swipeDistance}px)`,
  };

  return (
    <div
      className={styles.container}
      style={containerStyle}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 顶部导航栏和标签页 */}
      <div className={styles.tabs}>
        <div className={styles.backButton} onClick={navigateToHome}>
          <Icon name="arrow-left" fontSize={20} />
        </div>
        <div className={styles.tab} onClick={navigateToOrderRedeem}>
          {t('订单核销')}
        </div>
        <div className={styles.tabActive}>{t('现场收银')}</div>
        <div className={styles.setting} onClick={navigateToSetting}>
          <Icon name="setting" fontSize={20} />
        </div>
      </div>

      {/* 连续收银已开启提示 */}
      <div className={styles.continuousCollectionTip}>
        <Icon name="pc-info-circle" fontSize={16} color="#197afa" />
        <span>{t('连续收银已开启')}</span>
      </div>

      <AdditionCalculator
        input={amount}
        setInput={setAmount}
        onConfirm={(val) => handleConfirmAmount(val)}
      />

      {/* 扫码弹窗 */}
      <Drawer visible={showScanModal} onClose={handleCancelScan} className={styles.scanModal}>
        <div className={styles.scanContent}>
          {/* 未开始支付 */}
          {!paymentStatus && (
            <>
              <div className={styles.scanTitle}>
                {t('合计金额')}: ¥ {amount || '0.00'}
              </div>
              <div className={styles.scanArea}>
                {/* 扫码区域 */}
                <div className={styles.scanPlaceholder} onClick={handleStartScan}>
                  {/* 黑色区域，点击开始扫码 */}
                </div>
              </div>
              <Button type="primary" onClick={handleCancelScan} className={styles.scanCancelButton}>
                {t('取消扫码')}
              </Button>
            </>
          )}

          {/* 支付中 */}
          {paymentStatus === 'paying' && (
            <>
              <div className={styles.scanTitle}>
                {t('合计金额')}: ¥ {amount || '0.00'}
              </div>
              <div className={styles.payingStatus}>
                <div className={styles.loadingIcon}>
                  <Icon name="pc-loading" fontSize={24} />
                </div>
                <div className={styles.payingText}>{t('支付中...')}</div>
              </div>
              <div className={styles.countdownText}>
                {countdown}
                {t('秒后可取消支付')}
              </div>
              <Button
                type="primary"
                onClick={handleCancelPayment}
                className={styles.scanCancelButton}
                disabled={countdown > 0}
              >
                {t('取消支付')}
              </Button>
            </>
          )}

          {/* 支付成功或失败 */}
          {(paymentStatus === 'success' || paymentStatus === 'fail') && paymentResult && (
            <>
              <div className={styles.resultIcon}>
                {paymentStatus === 'success' ? (
                  <Icon name="pc-check-circle" fontSize={60} color="#52c41a" />
                ) : (
                  <Icon name="pc-close-circle" fontSize={60} color="#ff4d4f" />
                )}
              </div>
              <div className={styles.resultText}>
                {paymentStatus === 'success' ? t('收银成功') : t('收银失败')}
              </div>
              <div className={styles.resultAmount}>¥ {paymentResult.amount || '0.00'}</div>
              <Button
                type="primary"
                onClick={handleContinueCollection}
                className={styles.continueButton}
              >
                {t('继续收银')}
              </Button>
            </>
          )}
        </div>
      </Drawer>
    </div>
  );
};

export default OnSiteScanPay;
