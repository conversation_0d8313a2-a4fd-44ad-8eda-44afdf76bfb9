import { post } from '../_/utils/fetch';
// 导入 mock 数据
import cancelOrderMock from './mock/cancelOrder.json';
import getOrderStatusMock from './mock/getOrderStatus.json';
import scanPayMock from './mock/scanPay.json';

export interface IScanPayParams {
  /** 消费二维码 */
  qrcode: string;
  /** 设备编码 */
  snCode: string;
}

export interface IScanPayResponse {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 订单总金额 */
  totalAmount?: number;
  /** 钱包支付金额 */
  walletPayAmount?: number;
  /** 差额 */
  margin?: number;
  /** 微信支付单号 */
  weChatPayOrderNo?: string;
  /** 钱包支付单号 */
  walletOderNo?: string;
  /** 微信支付链接 */
  weChatPayUrl?: string;
}

/**
 * 食堂设备端扫码订单支付
 * https://soapi.sheincorp.cn/application/3694/routes/245373/doc
 * @param {IScanPayParams} params
 * @returns {Promise<IScanPayResponse>} 返回值
 */
export const scanPayAPI = (params: IScanPayParams) => {
  // 开发环境使用 mock 数据
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve(scanPayMock.data);
  }
  return post<IScanPayResponse>('/canteen/device/order/scanPay', params);
};

export interface ICancelOrderParams {
  /** 食堂订单号 */
  orderNo: string;
  /** 设备编码 */
  snCode: string;
}

/**
 * 食堂设备端订单取消
 * https://soapi.sheincorp.cn/application/3694/routes/245374/doc
 * @param {ICancelOrderParams} params
 * @returns {Promise<boolean>} 返回值
 */
export const cancelOrderAPI = (params: ICancelOrderParams) => {
  // 开发环境使用 mock 数据
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve(cancelOrderMock.data);
  }
  return post<boolean>('/canteen/device/order/cancel', params);
};

export interface IGetOrderStatusParams {
  /** 订单号 */
  orderNo: string;
  /** 设备sn码 */
  snCode: string;
}

export interface IGetOrderStatusResponse {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 订单状态 0待支付，1已预定，2已完成，3已取消，4已退款 */
  orderStatus?: string; // SOAPI 定义为 datetime，但实际业务中通常是状态码，这里用 string，具体根据后端调整
}

/**
 * 查询订单状态
 * https://soapi.sheincorp.cn/application/3694/routes/245722/doc
 * @param {IGetOrderStatusParams} params
 * @returns {Promise<IGetOrderStatusResponse>} 返回值
 */
export const getOrderStatusAPI = (params: IGetOrderStatusParams) => {
  // 开发环境使用 mock 数据
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve(getOrderStatusMock.data);
  }
  return post<IGetOrderStatusResponse>('/canteen/device/order/status', params);
};
