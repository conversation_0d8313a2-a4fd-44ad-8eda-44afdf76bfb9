import { useState } from 'react';
import {
  canteenCashierContinuousScanStorage,
  canteenContinuousScanVerificationStorage,
  canteenPaymentVoiceBroadcastStorage,
  canteenQuickAmountStorage,
} from '@/_/utils/storage';
import { t } from '@shein-bbl/react';
import { useSetState } from 'ahooks';
import { Cell, Switch } from 'shineout-mobile';
import styles from '../index.less';
import QuickAmountModal from './quick-amount-modal';

const SettingForm = (): React.ReactElement => {
  const [paymentVoiceBroadcast, setPaymentVoiceBroadcast] = useState(() => {
    return canteenPaymentVoiceBroadcastStorage.getItem() === '1';
  });
  const [cashierContinuousScan, setCashierContinuousScan] = useState(() => {
    return canteenCashierContinuousScanStorage.getItem() === '1';
  });
  const [continuousScanVerification, setContinuousScanVerification] = useState(() => {
    return canteenContinuousScanVerificationStorage.getItem() === '1';
  });
  const [quickAmountModal, setQuickAmountModal] = useSetState(() => {
    const amountList = canteenQuickAmountStorage.getItem() || [];
    return {
      visible: false,
      amountList: amountList.filter(Boolean),
    };
  });
  /**
   * @description 收款金额播报
   * @param {boolean} checked
   */
  const handlePaymentVoiceBroadcastChange = (checked: boolean) => {
    setPaymentVoiceBroadcast(checked);
    canteenPaymentVoiceBroadcastStorage.setItem(checked ? '1' : '0');
  };

  /**
   * @description 现场收银连续扫码
   * @param {boolean} checked
   */
  const handleCashierContinuousScanChange = (checked: boolean) => {
    setCashierContinuousScan(checked);
    canteenCashierContinuousScanStorage.setItem(checked ? '1' : '0');
  };

  /**
   * @description 连续扫码验证
   * @param {boolean} checked
   */
  const handleContinuousScanVerificationChange = (checked: boolean) => {
    setContinuousScanVerification(checked);
    canteenContinuousScanVerificationStorage.setItem(checked ? '1' : '0');
  };

  /**
   * @description 快捷金额设置
   * @param {string[]} value
   */
  const handleQuickAmountModalConfirm = (value: string[]) => {
    setQuickAmountModal({ visible: false, amountList: value });
    canteenQuickAmountStorage.setItem(value);
  };

  return (
    <div className="pt-[8px]">
      <Cell
        label={t('收款金额播报')}
        value={
          <Switch value={paymentVoiceBroadcast} onChange={handlePaymentVoiceBroadcastChange} />
        }
      />
      <Cell
        label={t('现场收银连续扫码')}
        value={
          <Switch value={cashierContinuousScan} onChange={handleCashierContinuousScanChange} />
        }
      />
      <Cell
        label={t('连续扫码验证')}
        value={
          <Switch
            value={continuousScanVerification}
            onChange={handleContinuousScanVerificationChange}
          />
        }
      />
      <Cell
        label={t('快捷金额设置')}
        value={
          quickAmountModal.amountList?.length > 0 ? (
            <div className="h-full flex items-center justify-end gap-[4px]">
              {quickAmountModal.amountList.map((item: string, index: number) => (
                // eslint-disable-next-line react/no-array-index-key
                <span key={index} className={styles.tag}>
                  ¥{item}
                </span>
              ))}
            </div>
          ) : (
            <div className="h-full flex items-center justify-end text-[#F56C0A]">{t('未设置')}</div>
          )
        }
        isLink
        onClick={() =>
          setQuickAmountModal({
            visible: true,
          })
        }
      />
      <QuickAmountModal
        visible={quickAmountModal.visible}
        value={quickAmountModal.amountList}
        onClose={() => setQuickAmountModal({ visible: false })}
        onConfirm={handleQuickAmountModalConfirm}
      />
    </div>
  );
};

export default SettingForm;
