.rawer {
  overflow: hidden;
}

.wrapper {
  display: flex;
  height: 100%;
  overflow: hidden;
  flex-direction: column;
  box-sizing: border-box;

  @supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    & {
      padding-bottom: constant(safe-area-inset-bottom); // 兼容 IOS < 11.2
      padding-bottom: env(safe-area-inset-bottom); // 兼容 IOS >= 11.2
    }
  }

  .title {
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #141737;
    text-align: center;
  }

  .list {
    padding: 12px;
    flex: 1;
    overflow-y: auto;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    padding: 12px;

    .btn {
      flex: 1;
    }
  }
}
