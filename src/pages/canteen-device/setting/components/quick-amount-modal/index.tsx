import { useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';
import Form from '@/_/components/Form';
import { t } from '@shein-bbl/react';
import { useUpdateEffect } from 'ahooks';
import { <PERSON><PERSON>, Drawer, Field } from 'shineout-mobile';
import styles from './index.less';

interface IQuickAmountModalProps {
  visible: boolean;
  value?: string[];
  onClose: () => void;
  onConfirm: (value: string[]) => void;
}

interface IQuickAmountModalForm {
  [key: string]: string;
}

const DEFAULT_AMOUNT_LIST = Array.from({ length: 4 }).fill('');

const QuickAmountModal = (props: IQuickAmountModalProps): React.ReactElement => {
  const { visible, onClose, value, onConfirm } = props;
  const formRef = useRef<UseFormReturn>();
  const submitButtonRef = useRef<HTMLButtonElement>(null);
  useUpdateEffect(() => {
    // 快捷金额可设置4个
    const amountList = [...(value || []), ...DEFAULT_AMOUNT_LIST].slice(0, 4);
    amountList.forEach((item, index) => {
      formRef.current?.setValue(`amountList${index}`, item);
    });
  }, [visible]);

  const handleSubmit = (data: IQuickAmountModalForm) => {
    onConfirm(
      DEFAULT_AMOUNT_LIST.map((_, index) => String(Number(data[`amountList${index}`]))).filter(
        Boolean,
      ),
    );
  };

  return (
    <Drawer
      round
      closeable
      position="bottom"
      drawerClass={styles.drawer}
      height={330}
      visible={visible}
      onClose={onClose}
    >
      <div className={styles.wrapper}>
        <div className={styles.title}>{t('设置快捷金额（元）')}</div>
        <div className={styles.list}>
          <Form formRef={formRef} onSubmit={handleSubmit}>
            {DEFAULT_AMOUNT_LIST.map((_, index) => (
              <Form.Field
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                name={`amountList${index}`}
                rules={{ required: t('请输入必填项') }}
              >
                {({ value, onChange, errorMessage }) => {
                  return (
                    <Field
                      value={value}
                      label={`${t('金额')}-${index + 1}`}
                      placeholder={t('请输入')}
                      type="number"
                      digits={2}
                      max={999.99}
                      min={1}
                      onBlur={(e) => {
                        const value = e.target.value;
                        if (+value < 1) {
                          onChange('1');
                        } else if (+value > 999.99) {
                          onChange('999.99');
                        } else {
                          onChange(value || '');
                        }
                      }}
                      align="right"
                      required
                      onChange={onChange}
                      errorMessage={<div className="text-right">{errorMessage}</div>}
                    />
                  );
                }}
              </Form.Field>
            ))}
            <button type="submit" ref={submitButtonRef} className="hidden" />
          </Form>
        </div>
        <div className={styles.footer}>
          <Button className="flex-1 bg-[#F4F5F8] border-none" onClick={onClose}>
            {t('取消')}
          </Button>
          <Button
            className="flex-1"
            type="primary"
            onClick={() => submitButtonRef.current?.click()}
          >
            {t('确定')}
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default QuickAmountModal;
