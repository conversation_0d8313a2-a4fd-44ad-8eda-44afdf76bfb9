import React, { useState } from 'react';
import { Page, PageLoading } from '@/_/components';
import { getDeviceSNCode, refreshDeviceSNCode } from '@/pages/canteen-device/_/utils/snCode';
import { t } from '@shein-bbl/react';
import { useMount, useRequest } from 'ahooks';
import { Button, Cell, Toast } from 'shineout-mobile';
import { getBindingInfoAPI } from '../_/services';
import SettingForm from './components/setting-form';
/**
 * @description 档口设置
 * @returns {React.ReactElement} 档口设置
 */
const StallSetting: React.FC = (): React.ReactElement => {
  const {
    data,
    loading,
    run: getBindingInfo,
    mutate: setBindingInfo,
  } = useRequest(getBindingInfoAPI, {
    manual: true,
    onError: () => {
      setBindingInfo(null);
    },
  });
  const [snCode, setSnCode] = useState<string | null>(null);

  /**
   * @description 重新获取SN码
   * @param {boolean} refresh 是否强制刷新
   */
  const handleGetSNCode = (refresh?: boolean) => {
    const snCode = refresh ? refreshDeviceSNCode() : getDeviceSNCode();
    setSnCode(snCode);
    if (snCode) {
      getBindingInfo({ snCode });
    } else {
      setBindingInfo(null);
      Toast.fail(t('无法获取设备SN码，请联系管理员'));
    }
  };

  useMount(() => {
    handleGetSNCode();
  });

  if (loading) {
    return <PageLoading title={t('设置')} />;
  }

  return (
    <Page title={t('设置')} contentClassName="p-0">
      <div>
        <Cell label={t('用户信息')} value={`${data?.staffName || ''}（${data?.workNum || ''}）`} />
        <Cell
          label={t('绑定档口')}
          value={`${data?.canteenName || ''} - ${data?.stallName || ''}`}
        />
        <Cell
          label={t('SN码')}
          value={
            <div>
              <span>{snCode}</span>
              <Button
                className="ml-[8px]"
                text
                type="primary"
                onClick={() => handleGetSNCode(true)}
              >
                {t('重新获取')}
              </Button>
            </div>
          }
        />
      </div>
      <SettingForm />
    </Page>
  );
};

export default StallSetting;
