/**
 * @file 食堂设备登录页面
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount, useUnmount } from 'ahooks';
import { Toast } from 'shineout-mobile';
import { getDeviceSNCode } from '../_/utils/snCode';
import { setCookie } from '../_/utils/token';
import logoPng from './assets/logo.png';
import { getLoginCodeAPI, getUlpTokenAPI } from './services';
import styles from './style.less';

// 二维码刷新时间
const RELOAD_TIME = 3 * 60 * 1000;

/**
 * 登录页面
 */
const Login: React.FC = () => {
  usePageTitle(t('希音食堂 Lite'));
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [alreadyBindingStall, setAlreadyBindingStall] = useState(false);
  const [loginCodeStr, setLoginCodeStr] = useState('');
  const [loginCodeId, setLoginCodeId] = useState('');
  const [snCode, setSnCode] = useState('');

  // 定时器引用
  const qrCodeRefreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const tokenCheckTimerRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 获取登录二维码
   */
  const fetchLoginCode = usePersistFn(async () => {
    try {
      const deviceSnCode = getDeviceSNCode();
      if (!deviceSnCode) {
        Toast.fail(t('无法获取设备sn码，请联系管理员'));
        return;
      }

      setSnCode(deviceSnCode);
      setLoading(true);

      const response = await getLoginCodeAPI({ snCode: deviceSnCode });

      setAlreadyBindingStall(response.alreadyBindingStall);
      if (response.alreadyBindingStall) {
        setLoginCodeStr(response.loginCodeStr);
        setLoginCodeId(response.loginCodeId);
      }
    } catch (error) {
      console.error(t('获取登录二维码失败:'), error);
      Toast.fail(t('获取登录二维码失败，请重试'));
    } finally {
      setLoading(false);
    }
  });

  /**
   * 检查登录状态
   */
  const checkLoginStatus = usePersistFn(async () => {
    if (!loginCodeId) return;

    try {
      const token = await getUlpTokenAPI({ loginCodeId });
      if (token) {
        // 登录成功，保存token并跳转
        setCookie(token);
        Toast.success(t('登录成功'));
        navigate('/canteen-device/home');
      }
    } catch (error) {
      // 忽略错误，继续轮询
      console.log(t('检查登录状态:'), error);
    }
  });

  /**
   * 设置定时刷新二维码
   */
  const setupQrCodeRefresh = useCallback(() => {
    // 清除之前的定时器
    if (qrCodeRefreshTimerRef.current) {
      clearTimeout(qrCodeRefreshTimerRef.current);
    }

    // 每3分钟刷新一次二维码
    const refreshQrCode = () => {
      fetchLoginCode();
      qrCodeRefreshTimerRef.current = setTimeout(() => {
        refreshQrCode();
      }, RELOAD_TIME);
    };

    // 启动定时刷新
    qrCodeRefreshTimerRef.current = setTimeout(refreshQrCode, RELOAD_TIME);
  }, [fetchLoginCode]);

  /**
   * 设置定时检查登录状态
   */
  const setupTokenCheck = useCallback(() => {
    // 清除之前的定时器
    if (tokenCheckTimerRef.current) {
      clearTimeout(tokenCheckTimerRef.current);
    }

    // 每秒检查一次登录状态
    const checkToken = () => {
      checkLoginStatus();
      tokenCheckTimerRef.current = setTimeout(() => {
        checkToken();
      }, 1000);
    };

    // 启动定时检查
    checkToken();
  }, [checkLoginStatus]);

  // 组件挂载时获取登录二维码
  useMount(() => {
    fetchLoginCode();
  });

  // 当二维码状态变化时，设置相应的定时器
  useEffect(() => {
    if (alreadyBindingStall && loginCodeId) {
      setupQrCodeRefresh();
      setupTokenCheck();
    }

    return () => {
      // 清除定时器
      if (qrCodeRefreshTimerRef.current) {
        clearTimeout(qrCodeRefreshTimerRef.current);
      }
      if (tokenCheckTimerRef.current) {
        clearTimeout(tokenCheckTimerRef.current);
      }
    };
  }, [alreadyBindingStall, loginCodeId, setupQrCodeRefresh, setupTokenCheck]);

  // 组件卸载时清除所有定时器
  useUnmount(() => {
    if (qrCodeRefreshTimerRef.current) {
      clearTimeout(qrCodeRefreshTimerRef.current);
    }
    if (tokenCheckTimerRef.current) {
      clearTimeout(tokenCheckTimerRef.current);
    }
  });

  // /**
  //  * 手动刷新二维码
  //  */
  // const handleRefreshQrCode = usePersistFn(() => {
  //   fetchLoginCode();
  // });

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {!alreadyBindingStall && (
          <div className={styles.logo}>
            <img className={styles.logoImg} src={logoPng} alt="logo" />
            <div className={styles.logoText}>{t('希音食堂 Lite')}</div>
          </div>
        )}
        {loading ? (
          <div className={styles.loading}>
            <Icon name="pc-loading" fontSize={40} />
          </div>
        ) : alreadyBindingStall ? (
          <div className={styles.qrCodeContainer}>
            <QRCode value={loginCodeStr} size={200} status="active" />
            <div className={styles.qrCodeTip}>{t('请打开园区通-我的-扫一扫')}</div>
            <div>{t('扫码登录')}</div>
            {/* <div className={styles.refreshTip} onClick={handleRefreshQrCode}>
              {t('点击刷新二维码')}
            </div> */}
            <div className={styles.snCodeInfo} style={{ marginTop: '12px' }}>
              {t('SN码')}: {snCode}
            </div>
          </div>
        ) : (
          <div className={styles.notBound}>
            <div>{t('设备未绑定档口')}</div>
            <div className={styles.notBoundTip}>{t('请联系园区行政绑定再重试')}</div>
            <div className={styles.snCodeInfo}>
              {t('SN码')}: {snCode}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Login;
