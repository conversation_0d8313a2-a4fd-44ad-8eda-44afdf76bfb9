import { useCallback, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount, useUnmount } from 'ahooks';
import { Button, Dialog, Toast } from 'shineout-mobile';
import styles from './index.less';
import { IConsumerBalanceInfo } from './interfaces';
import { getConsumerBalanceAPI, getConsumerQrcodeAPI } from './serivces';

// 刷新二维码的间隔时间（毫秒）
const INTERVAL_TIME = 3 * 60 * 1000; // 3分钟

const ConsumerQrcodePage = () => {
  usePageTitle(t('消费码'));
  const [loading, setLoading] = useState(false);
  const [codeValue, setCodeValue] = useState('');
  const [balanceInfo, setBalanceInfo] = useState<IConsumerBalanceInfo>({});
  const intervalRef = useRef<NodeJS.Timeout>();

  const startIntervalGetCode = usePersistFn(() => {
    intervalRef.current = setInterval(() => {
      handleGetConsumerQrcode();
    }, INTERVAL_TIME);
  });

  /**
   * @description 获取消费码
   */
  const handleGetConsumerQrcode = () => {
    setLoading(true);
    getConsumerQrcodeAPI({
      refresh: true,
    })
      .then((res) => {
        setCodeValue(res);
      })
      .catch(() => {
        Toast.fail(t('获取消费码失败'));
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description 获取钱包余额信息
   */
  const handleGetBalanceInfo = useCallback(() => {
    getConsumerBalanceAPI()
      .then((res) => {
        setBalanceInfo(res);
      })
      .catch(() => {
        Toast.fail(t('获取余额信息失败'));
      });
  }, []);

  useMount(() => {
    handleGetBalanceInfo();
    handleGetConsumerQrcode();
    startIntervalGetCode();
  });

  useUnmount(() => {
    clearInterval(intervalRef.current);
  });

  /**
   * @description 刷新二维码
   */
  const handleRefreshCode = () => {
    clearInterval(intervalRef.current);
    handleGetConsumerQrcode();
    startIntervalGetCode();
  };

  /**
   * @description 查看钱包余额明细
   */
  const handleViewBalanceDetail = () => {
    if (!balanceInfo.detail || balanceInfo.detail.length === 0) {
      Toast.info(t('暂无余额明细'));
      return;
    }

    Dialog.alert({
      title: t('钱包余额明细'),
      message: (
        <div className={styles.balanceDetailDialog}>
          <div className={styles.totalBalance}>
            <div className={styles.label}>{t('钱包总余额')}</div>
            <div className={styles.value}>¥{balanceInfo.total?.toFixed(2) || '--'}</div>
          </div>
          {balanceInfo.detail?.map((item, index) => (
            <div key={index} className={styles.detailItem}>
              <div className={styles.accountName}>{item.accountName}</div>
              <div className={styles.accountBalance}>
                ¥{item.accountBalance?.toFixed(2) || '0.00'}
              </div>
            </div>
          ))}
        </div>
      ),
      confirmButtonText: t('我知道了'),
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.code}>
        <QRCode
          size={220}
          className={styles.qrCode}
          status={loading ? 'loading' : 'active'}
          value={codeValue}
        />
        <div className={styles.subDes}>
          <p>
            <Button text type="primary" onClick={handleRefreshCode}>
              {t('点击刷新消费码')}
            </Button>
          </p>
        </div>
      </div>
      <div style={{ padding: '0 12px 12px', background: '#fff' }}>
        <div className={styles.walletInfo}>
          <div className={styles.walletCard}>
            <div className={styles.title}>
              <div style={{ fontSize: '20px', color: '#f56c0a', marginRight: '6px' }}>
                <Icon name="pc-charge-shineout-fill" />
              </div>
              {t('钱包余额')}
            </div>
            <div className={styles.balanceRow}>
              <div className={styles.balanceAmount}>¥{balanceInfo.total?.toFixed(2) || '0.00'}</div>
              <Button
                text
                type="primary"
                className={styles.detailButton}
                onClick={handleViewBalanceDetail}
              >
                <Icon name="help-outline" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsumerQrcodePage;
