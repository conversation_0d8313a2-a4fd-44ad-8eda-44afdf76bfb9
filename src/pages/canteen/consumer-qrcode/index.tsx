import { useCallback, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import RemainingWXPay from '@/pages/canteen/_/components/remaining-wx-pay';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useMount, useUnmount } from 'ahooks';
import { Button, Dialog, Toast } from 'shineout-mobile';
import { useCanteenPay } from '../_/hooks/use-canteen-pay';
import { ICanteenPayResponse } from '../_/interfaces';
import styles from './index.less';
import { IConsumerBalanceInfo, IConsumerResultResponse } from './interfaces';
import { getConsumerBalanceAPI, getConsumerQrcodeAPI, getConsumerResultAPI } from './serivces';

// 刷新二维码的间隔时间（毫秒）
const INTERVAL_TIME = 3 * 60 * 1000; // 3分钟
// 轮询消费结果的间隔时间（毫秒）
const POLL_INTERVAL = 1000; // 1秒

const ConsumerQrcodePage = () => {
  usePageTitle(t('消费码'));
  const [loading, setLoading] = useState(false);
  const [codeValue, setCodeValue] = useState('');
  const [balanceInfo, setBalanceInfo] = useState<IConsumerBalanceInfo>({});
  const intervalRef = useRef<NodeJS.Timeout>();
  const pollTimerRef = useRef<NodeJS.Timeout>();

  // 支付相关状态
  const [remainingPay, setRemainingPay] = useState<{
    visible: boolean;
    info?: ICanteenPayResponse;
  }>({
    visible: false,
    info: undefined,
  });
  const [orderNo, setOrderNo] = useState<string>('');

  const { handlePayWechat, payLoading } = useCanteenPay();

  const startIntervalGetCode = usePersistFn(() => {
    intervalRef.current = setInterval(() => {
      handleGetConsumerQrcode();
    }, INTERVAL_TIME);
  });

  /**
   * @description 开始轮询消费结果
   */
  const startPollingConsumerResult = usePersistFn(() => {
    const poll = () => {
      getConsumerResultAPI()
        .then((res: IConsumerResultResponse) => {
          if (Number(res.paySuccess) === 1) {
            // 支付成功，显示提示并刷新消费码
            Toast.success(t('支付成功'));
            handleGetConsumerQrcode();
            handleGetBalanceInfo();
          } else if (Number(res.paySuccess) === 2) {
            // 需要微信支付，显示支付弹窗
            setOrderNo(res.orderNo || '');
            setRemainingPay({
              visible: true,
              info: {
                margin: res.wechatPay || 0,
                totalAmount: res.totalPay || 0,
                walletPayAmount: res.walletPay || 0,
              },
            });
          }
          // paySuccess为0时无需处理，继续轮询
        })
        .catch((error: unknown) => {
          console.error(t('轮询消费结果失败:'), error);
        })
        .finally(() => {
          // 使用setTimeout而非setInterval实现轮询
          pollTimerRef.current = setTimeout(poll, POLL_INTERVAL);
        });
    };

    // 开始轮询
    poll();
  });

  /**
   * @description 停止轮询消费结果
   */
  const stopPollingConsumerResult = usePersistFn(() => {
    if (pollTimerRef.current) {
      clearTimeout(pollTimerRef.current);
      pollTimerRef.current = undefined;
    }
  });

  /**
   * @description 获取消费码
   */
  const handleGetConsumerQrcode = () => {
    setLoading(true);
    getConsumerQrcodeAPI({
      refresh: true,
    })
      .then((res: string) => {
        setCodeValue(res);
      })
      .catch(() => {
        Toast.fail(t('获取消费码失败'));
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description 获取钱包余额信息
   */
  const handleGetBalanceInfo = useCallback(() => {
    getConsumerBalanceAPI()
      .then((res: IConsumerBalanceInfo) => {
        setBalanceInfo(res);
      })
      .catch(() => {
        Toast.fail(t('获取余额信息失败'));
      });
  }, []);

  /**
   * @description 继续支付
   */
  const handleContinuePay = usePersistFn(() => {
    if (orderNo) {
      handlePayWechat(orderNo);
    }
  });

  /**
   * @description 取消支付成功
   */
  const handleCancelSuccess = usePersistFn(() => {
    setRemainingPay({ visible: false, info: undefined });
    setOrderNo('');
    // 取消支付后刷新消费码和余额信息
    handleGetConsumerQrcode();
    handleGetBalanceInfo();
  });

  useMount(() => {
    handleGetBalanceInfo();
    handleGetConsumerQrcode();
    startIntervalGetCode();
    startPollingConsumerResult();
  });

  useUnmount(() => {
    clearInterval(intervalRef.current);
    stopPollingConsumerResult();
  });

  /**
   * @description 刷新二维码
   */
  const handleRefreshCode = () => {
    clearInterval(intervalRef.current);
    handleGetConsumerQrcode();
    startIntervalGetCode();
  };

  /**
   * @description 查看钱包余额明细
   */
  const handleViewBalanceDetail = () => {
    if (!balanceInfo.detail || balanceInfo.detail.length === 0) {
      Toast.info(t('暂无余额明细'));
      return;
    }

    Dialog.alert({
      title: t('钱包余额明细'),
      message: (
        <div className={styles.balanceDetailDialog}>
          <div className={styles.totalBalance}>
            <div className={styles.label}>{t('钱包总余额')}</div>
            <div className={styles.value}>¥{balanceInfo.total?.toFixed(2) || '--'}</div>
          </div>
          {balanceInfo.detail?.map((item, index) => (
            <div key={item.accountName || `account-${index}`} className={styles.detailItem}>
              <div className={styles.accountName}>{item.accountName}</div>
              <div className={styles.accountBalance}>
                ¥{item.accountBalance?.toFixed(2) || '0.00'}
              </div>
            </div>
          ))}
        </div>
      ),
      confirmButtonText: t('我知道了'),
    });
  };

  return (
    <div className={styles.container}>
      <div className={styles.code}>
        <QRCode
          size={220}
          className={styles.qrCode}
          status={loading ? 'loading' : 'active'}
          value={codeValue}
        />
        <div className={styles.subDes}>
          <p>
            <Button text type="primary" onClick={handleRefreshCode}>
              {t('点击刷新消费码')}
            </Button>
          </p>
        </div>
      </div>
      <div style={{ padding: '0 12px 12px', background: '#fff' }}>
        <div className={styles.walletInfo}>
          <div className={styles.walletCard}>
            <div className={styles.title}>
              <div style={{ fontSize: '20px', color: '#f56c0a', marginRight: '6px' }}>
                <Icon name="pc-charge-shineout-fill" />
              </div>
              {t('钱包余额')}
            </div>
            <div className={styles.balanceRow}>
              <div className={styles.balanceAmount}>¥{balanceInfo.total?.toFixed(2) || '0.00'}</div>
              <Button
                text
                type="primary"
                className={styles.detailButton}
                onClick={handleViewBalanceDetail}
              >
                <Icon name="help-outline" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      <RemainingWXPay
        visible={remainingPay.visible}
        onCancelSuccess={handleCancelSuccess}
        onConfirm={handleContinuePay}
        margin={remainingPay.info?.margin}
        totalAmount={remainingPay.info?.totalAmount}
        walletPayAmount={remainingPay.info?.walletPayAmount}
        orderNo={orderNo}
        loading={payLoading}
      />
    </div>
  );
};

export default ConsumerQrcodePage;
