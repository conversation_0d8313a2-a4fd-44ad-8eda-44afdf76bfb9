interface IConsumerQrcodeRequestBody {
  /** true刷新，false或null不刷新 */
  refresh?: boolean;
}

interface IConsumerBalanceInfoDetailItem {
  /** 账户名称 */
  accountName?: string;
  /** 账户金额 */
  accountBalance?: number;
}

interface IConsumerBalanceInfo {
  /** 总余额 */
  total?: number;
  /** 详情 */
  detail?: IConsumerBalanceInfoDetailItem[];
}

export interface IConsumerResultResponse {
  /** 支付状态：0无结果，1支付成功，2微信支付 */
  paySuccess: 0 | 1 | 2;
  /** 订单ID */
  orderId?: number;
  /** 订单号 */
  orderNo?: string;
  /** 合计需支付金额 */
  totalPay?: number;
  /** 钱包支付金额 */
  walletPay?: number;
  /** 微信支付金额 */
  wechatPay?: number;
}

export type { IConsumerBalanceInfo, IConsumerBalanceInfoDetailItem, IConsumerQrcodeRequestBody };
